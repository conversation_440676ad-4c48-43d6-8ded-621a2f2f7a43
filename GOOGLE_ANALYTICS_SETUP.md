# Google Analytics Setup

This document explains how Google Analytics has been integrated into the Sonata Sites project with proper cookie consent management.

## Overview

Google Analytics (GA4) has been added to track user interactions while respecting user privacy through a comprehensive cookie consent system.

## Implementation Details

### 1. Environment Variables

The Google Analytics Measurement ID is stored securely in `.env.local`:

```
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-ZPBF0G5P8J
```

**Important**: The `.env.local` file is automatically ignored by G<PERSON> (as specified in `.gitignore`) to keep your analytics ID secure.

### 2. Components

#### GoogleAnalytics Component (`src/components/GoogleAnalytics.tsx`)
- Conditionally loads Google Analytics scripts based on user consent
- Listens for consent changes and updates tracking accordingly
- Uses Next.js Script component for optimal loading

#### Updated CookieConsent Component (`src/components/CookieConsent.tsx`)
- Updated to mention Google Analytics in the analytics preference description
- Handles Google Analytics consent state changes
- Integrates with existing Vercel Analytics consent management

### 3. Analytics Utilities (`src/lib/analytics.ts`)

Provides helper functions for tracking:
- `hasAnalyticsConsent()` - Check if user has consented to analytics
- `trackPageView()` - Track page views
- `trackEvent()` - Track custom events
- `trackFormSubmission()` - Track form submissions
- `trackButtonClick()` - Track button clicks
- `trackDownload()` - Track file downloads

### 4. Layout Integration (`src/app/layout.tsx`)

The GoogleAnalytics component is conditionally rendered in the root layout only when:
- The measurement ID environment variable is present
- The user has consented to analytics cookies

## Privacy Compliance

### Cookie Consent Flow

1. **Initial Visit**: Cookie consent banner appears after 1 second
2. **User Choice**: User can accept all, reject all, or customize preferences
3. **Analytics Consent**: Google Analytics only loads if user consents to analytics cookies
4. **Consent Changes**: If user changes preferences, Google Analytics is enabled/disabled accordingly

### GDPR Compliance

- ✅ **Consent Before Tracking**: No analytics scripts load until user consents
- ✅ **Granular Control**: Users can specifically opt-out of analytics while keeping necessary cookies
- ✅ **Consent Management**: Users can change their preferences at any time
- ✅ **Data Minimization**: Only essential tracking is implemented

## Usage Examples

### Basic Page Tracking
Page views are automatically tracked when the GoogleAnalytics component is loaded and user has consented.

### Custom Event Tracking
```typescript
import { trackEvent, trackButtonClick, trackFormSubmission } from '@/lib/analytics';

// Track a custom event
trackEvent('video_play', 'engagement', 'homepage_hero_video');

// Track button clicks
trackButtonClick('Get Quote', 'header');

// Track form submissions
trackFormSubmission('contact_form');
```

### Checking Consent Status
```typescript
import { hasAnalyticsConsent } from '@/lib/analytics';

if (hasAnalyticsConsent()) {
  // Perform analytics-related actions
}
```

## Testing

### Development Testing
1. Start the development server: `npm run dev`
2. Open browser developer tools
3. Go to Application > Local Storage
4. Clear any existing `cookie-consent` entries
5. Refresh the page
6. Accept analytics cookies in the consent banner
7. Check Network tab for Google Analytics requests to `googletagmanager.com`

### Production Testing
1. Deploy to production
2. Use Google Analytics Real-Time reports to verify tracking
3. Test consent flow in incognito/private browsing mode

## Security Notes

- The Google Analytics Measurement ID is stored in environment variables
- The `.env.local` file is gitignored to prevent accidental commits
- Only the `NEXT_PUBLIC_` prefixed variable is exposed to the client
- All tracking respects user consent preferences

## Maintenance

### Updating the Measurement ID
1. Update the value in `.env.local`
2. Restart the development server
3. Deploy the changes

### Adding New Tracking Events
1. Use the utility functions in `src/lib/analytics.ts`
2. Always check for consent before tracking
3. Follow Google Analytics event naming conventions
