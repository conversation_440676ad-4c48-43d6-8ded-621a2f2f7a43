
const puppeteer = require('puppeteer');
const { spawn } = require('child_process');
const waitOn = require('wait-on');
const fs = require('fs');
const path = require('path');

const DEV_URL = 'http://localhost:3000';
const ROUTES = [
    '/',
    '/about', 
    '/contact',
    '/process',
    '/faq',
    '/additional-services',
    '/testimonials'
]; // All available pages in your Next.js app
const SCREENSHOT_DIR = path.join(__dirname, 'screenshots');

(async () => {
    // Step 1: Create /screenshots folder if it doesn't exist
    if (!fs.existsSync(SCREENSHOT_DIR)) {
        fs.mkdirSync(SCREENSHOT_DIR);
        console.log('Created screenshots folder at root.');
    }

    console.log(puppeteer.executablePath());

    // Step 2: Start dev server
    console.log('Starting dev server...');
    const devProcess = spawn('npm', ['run', 'dev'], {
        shell: true,
        cwd: __dirname,
        stdio: 'inherit'
    });

    // Step 3: Wait until server is up
    try {
        await waitOn({
            resources: [DEV_URL],
            timeout: 30000, // 30 seconds
        });
    } catch (err) {
        console.error('Dev server did not start in time.');
        devProcess.kill();
        process.exit(1);
    }

    console.log('Dev server is up! Starting browser...');

    const browser = await puppeteer.launch();
    const page = await browser.newPage();

    for (const route of ROUTES) {
        const url = `${DEV_URL}${route}`;
        console.log(`Visiting ${url}...`);

        await page.setViewport({
            width: 1520,  // or whatever your preferred desktop width is
            height: 836, // initial height; we’ll scroll anyway
        });


        await page.goto(url, { waitUntil: 'networkidle2' });

        await new Promise(resolve => setTimeout(resolve, 1000)); // wait for animation to load 

        // Hide fixed navbar if needed
        await page.evaluate(() => {
            const style = document.createElement('style');
            style.innerHTML = `
              .header {
                position: static !important;
                top: unset !important;
              }
            `;
            document.head.appendChild(style);
        });

        await autoScroll(page);

        await new Promise(resolve => setTimeout(resolve, 1000));

        const sanitizedName = route === '/' ? 'home' : route.replace(/\//g, '_');
        const filePath = path.join(SCREENSHOT_DIR, `${sanitizedName}.png`);

        await page.screenshot({ path: filePath, fullPage: true });
        console.log(`📸 Screenshot saved: ${filePath}`);
    }

    await browser.close();
    devProcess.kill();
    console.log('All done!');
})();

// Scroll down to trigger lazy loading or effects
async function autoScroll(page) {
    await page.evaluate(async () => {
        await new Promise((resolve) => {
            let totalHeight = 0;
            const distance = window.innerHeight;
            const timer = setInterval(() => {
                window.scrollBy(0, distance);
                totalHeight += distance;
                if (totalHeight >= document.body.scrollHeight) {
                    clearInterval(timer);
                    resolve();
                }
            }, 250);
        });
    });
}
