"use client";

import { motion } from "framer-motion";
import Header from "../../components/Header";
import Footer from "../../components/Footer";

export default function About() {
  return (
    <>
      <Header />
      {/* Hero Section with purple gradient overlay and angled clip-path */}
      <section
        className="relative min-h-screen flex items-center justify-center"
        style={{
          backgroundImage:
            "linear-gradient(75deg, rgba(17, 17, 17, 0.9) 0%, rgba(74, 0, 224, 0.7) 100%), url('https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=1200&q=80')",
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="relative z-10 w-full max-w-4xl mx-auto px-4 py-20 flex flex-col items-center text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            className="inline-block bg-white text-purple-700 font-bold rounded-full px-6 py-2 mb-6 shadow-lg text-lg"
          >
            About Us
          </motion.div>
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.1 }}
            className="text-4xl md:text-5xl font-extrabold text-white mb-4 drop-shadow-lg"
          >
            Empowering Your Digital Presence
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="text-white text-lg md:text-xl mb-8 max-w-2xl mx-auto"
          >
            At Sonata Sites, we specialize in creating modern, responsive, and scalable websites tailored to your business needs. With a passion for innovation and a commitment to excellence, we turn your ideas into reality.
          </motion.p>
          <motion.a
            href="/services"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            className="inline-block bg-black text-white font-semibold px-8 py-3 rounded-full shadow-lg hover:bg-purple-800 hover:scale-105 transition text-lg"
          >
            Our Services
          </motion.a>
        </div>
      </section>

      {/* Story, Mission, Values Section */}
      <main className="bg-white py-20 px-4">
        <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -40 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7 }}
            className="space-y-8"
          >
            <div>
              <h2 className="text-2xl font-bold text-purple-700 mb-2">Our Story</h2>
              <p className="text-indigo-900 text-lg">
                We started just like you – posting on Craigslist, doing good work, but struggling to look professional online. We learned the hard way that great work isn&apos;t enough if customers can&apos;t find you or don&apos;t trust you online.
              </p>
              <p className="text-indigo-900 text-lg mt-2">
                That&apos;s why we created Sonata Sites. We know exactly what small business owners need because we&apos;ve been there.
              </p>
            </div>
            <div>
              <h2 className="text-2xl font-bold text-purple-700 mb-2">Our Mission</h2>
              <p className="text-indigo-900 text-lg">
                Help hardworking business owners compete with the big guys by giving them professional websites that actually work – without the big agency prices or monthly fees.
              </p>
            </div>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 40 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="flex flex-col gap-8"
          >
            <div className="bg-purple-50 rounded-2xl shadow-lg p-8">
              <h3 className="text-xl font-semibold text-purple-700 mb-2">What Makes Us Different</h3>
              <ul className="list-disc pl-6 text-indigo-900 text-base space-y-1">
                <li>We only work with small businesses (no Fortune 500 companies)</li>
                <li>We speak plain English, not tech jargon</li>
                <li>We deliver fast because we know time is money</li>
                <li>We focus on getting you customers, not winning design awards</li>
              </ul>
            </div>
            <div className="bg-gradient-to-br from-purple-100 to-white rounded-2xl shadow-lg p-8 border-2 border-purple-200">
              <h3 className="text-xl font-semibold text-purple-700 mb-2">Our Promise</h3>
              <p className="text-indigo-900 text-base">
                Every website we build is designed to do one thing: get you more customers. If it doesn&apos;t work, we&apos;ll fix it for free.
              </p>
            </div>
          </motion.div>
        </div>
      </main>
      <Footer />
    </>
  );
}
