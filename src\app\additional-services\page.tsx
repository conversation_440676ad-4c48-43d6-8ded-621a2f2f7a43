"use client";

import { motion } from "framer-motion";
import Image from "next/image";                       // ⬅️ NEW
import Header from "../../components/Header";
import Footer from "../../components/Footer";

const services = [
  {
    title: "Logo Design",
    price: "$99",
    description:
      "Professional logo that works on everything from business cards to vehicle wraps.",
    image:
      "https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80",
  },
  {
    title: "Business Cards",
    price: "$49",
    description:
      "100 premium business cards designed and shipped to your door.",
    image:
      "https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80",
  },
  {
    title: "Flyers & Marketing Materials",
    price: "$75",
    description:
      "Eye-catching flyers, door hangers, or promotional materials.",
    image:
      "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?auto=format&fit=crop&w=800&q=80",
  },
  {
    title: "Social Media Graphics",
    price: "$149",
    description:
      "Facebook, Instagram, and Google Business Profile graphics that match your brand.",
    image:
      "https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80",
  },
  {
    title: "Google Business Profile Setup",
    price: "$99",
    description:
      "Complete setup and optimization so you show up when people search locally.",
    image: "https://picsum.photos/seed/googlebiz/800/600",
  },
  {
    title: "Website Maintenance",
    price: "$29/mo",
    description:
      "We handle updates, security, and backups so you don't have to worry about anything.",
    image: "https://picsum.photos/seed/maintenance/800/600",
  },
  {
    title: "Google Ads Consultation",
    price: "$199",
    description:
      "One-hour session to set up Google Ads that actually make you money.",
    image: "https://picsum.photos/seed/googleads/800/600",
  },
];

export default function AdditionalServices() {
  return (
    <>
      <Header />

      {/* Hero Section */}
      <section
        className="relative min-h-screen flex items-center justify-center"
        style={{
          backgroundImage:
            "linear-gradient(75deg, rgba(17, 17, 17, 0.9) 0%, rgba(74, 0, 224, 0.7) 100%), url('https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=1200&q=80')",
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="relative z-10 w-full max-w-3xl mx-auto px-4 py-20 flex flex-col items-center text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            className="inline-block bg-white text-purple-700 font-bold rounded-full px-6 py-2 mb-6 shadow-lg text-lg"
          >
            More Ways to Grow Your Business
          </motion.div>
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.1 }}
            className="text-4xl md:text-5xl font-extrabold text-white mb-4 drop-shadow-lg"
          >
            Expand Your Brand with Our Additional Services
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="text-white text-lg md:text-xl mb-8 max-w-2xl mx-auto"
          >
            From logos to Google Ads, we offer everything you need to look
            professional and grow your business online and offline.
          </motion.p>
        </div>
      </section>

      {/* Services Alternating Layout Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="max-w-5xl mx-auto flex flex-col gap-16">
          {services.map((service, idx) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.2 }}
              transition={{ duration: 0.7, delay: idx * 0.1 }}
              className={`flex flex-col md:flex-row ${
                idx % 2 === 1 ? "md:flex-row-reverse" : ""
              } bg-white rounded-3xl shadow-xl overflow-hidden`}
              style={{
                background:
                  idx % 2 === 1
                    ? "linear-gradient(90deg, #ede9fe 0%, #fff 100%)"
                    : "white",
              }}
            >
              {/* ⬇️ Replaced <img> with <Image /> */}
              <Image
                src={service.image}
                alt={service.title}
                width={800}              // Next.js needs an explicit size
                height={600}
                className="w-full md:w-1/2 h-64 object-cover object-center"
                priority={idx < 2}       // eager‑load first couple for better LCP
              />
              <div className="flex-1 p-10 flex flex-col justify-center">
                <h2 className="text-2xl font-bold text-purple-700 mb-2">
                  {service.title}
                </h2>
                <span className="inline-block bg-gradient-to-r from-purple-500 to-indigo-700 text-white text-sm font-semibold px-4 py-1 rounded-full mb-3 shadow">
                  {service.price}
                </span>
                <p className="text-indigo-900 text-lg mb-2">
                  {service.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="bg-gradient-to-r from-purple-500 to-indigo-700 py-16 text-center">
        <motion.h2
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.7 }}
          className="text-3xl md:text-4xl font-extrabold text-white mb-4"
        >
          Ready to Supercharge Your Business?
        </motion.h2>
        <motion.p
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.7, delay: 0.1 }}
          className="text-white text-lg mb-8"
        >
          Contact us today for a free consultation and let&apos;s take your
          brand to the next level.
        </motion.p>
        <motion.a
          href="/contact"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.7, delay: 0.2 }}
          className="inline-block bg-white text-purple-700 font-semibold px-10 py-4 rounded-full shadow-lg hover:bg-purple-100 hover:scale-105 transition text-lg"
        >
          Contact Us
        </motion.a>
      </section>

      <Footer />
    </>
  );
}
