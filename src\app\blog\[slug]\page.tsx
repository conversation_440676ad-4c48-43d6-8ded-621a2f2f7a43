"use client";

import { useState, useEffect } from 'react';
import { getPostBySlug, getRelatedPosts, formatDate } from '../../../lib/blog';
import { useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import Header from '../../../components/Header';
import Footer from '../../../components/Footer';
import BlogCard from '../../../components/blog/BlogCard';
import ShareButtons from '../../../components/blog/ShareButtons';
import StructuredData from '../../../components/blog/StructuredData';
import MarkdownRenderer from '../../../components/blog/MarkdownRenderer';
import LoadingSpinner from '../../../components/LoadingSpinner';
import { BlogPost } from '../../../types/blog';

export default function BlogPostPage() {
  const params = useParams();
  const slug = params.slug as string;
  const [post, setPost] = useState<BlogPost | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPost = async () => {
      setLoading(true);
      try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 300));

        const foundPost = getPostBySlug(slug);
        if (foundPost) {
          setPost(foundPost);
          setRelatedPosts(getRelatedPosts(foundPost));
        }
      } catch (error) {
        console.error('Error fetching post:', error);
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchPost();
    }
  }, [slug]);

  if (loading) {
    return (
      <>
        <Header />
        <div className="min-h-screen bg-white">
          <section className="pt-32 pb-32 bg-white">
            <div className="max-w-4xl mx-auto px-8 text-center">
              <LoadingSpinner size="lg" />
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="text-gray-600 mt-6 text-lg"
              >
                Loading article...
              </motion.p>
            </div>
          </section>
        </div>
        <Footer />
      </>
    );
  }

  if (!post) {
    return (
      <>
        <Header />
        <div className="min-h-screen bg-white">
          <div className="max-w-4xl mx-auto px-8 py-16 text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Article Not Found
            </h1>
            <p className="text-gray-600 mb-8">
              The article you&apos;re looking for doesn&apos;t exist or has been moved.
            </p>
            <Link
              href="/blog"
              className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition"
            >
              Back to Blog
            </Link>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <StructuredData post={post} />
      <Header />
      <motion.article
        className="min-h-screen bg-white"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {/* Article Header */}
        <section className="bg-white border-b border-gray-200 pt-32">
          <div className="max-w-4xl mx-auto px-8 py-16">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
            >
              {/* Breadcrumb */}
              <nav className="mb-8">
                <ol className="flex items-center space-x-2 text-sm text-gray-500">
                  <li><Link href="/" className="hover:text-purple-600">Home</Link></li>
                  <li>/</li>
                  <li><Link href="/blog" className="hover:text-purple-600">Blog</Link></li>
                  <li>/</li>
                  <li className="text-gray-800">{post.title}</li>
                </ol>
              </nav>

              {/* Category Badge */}
              <div className="mb-4">
                <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium text-white ${post.category.color}`}>
                  {post.category.name}
                </span>
              </div>

              {/* Title */}
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                {post.title}
              </h1>

              {/* Meta Info */}
              <div className="flex flex-wrap items-center gap-6 text-gray-600 mb-8">
                <div className="flex items-center gap-3">
                  <Image
                    src={post.author.avatar}
                    alt={post.author.name}
                    width={40}
                    height={40}
                    className="rounded-full"
                  />
                  <span className="font-medium">{post.author.name}</span>
                </div>
                <time dateTime={post.publishedAt}>
                  {formatDate(post.publishedAt)}
                </time>
                <span>{post.readTime} min read</span>
              </div>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-8">
                {post.tags.map((tag) => (
                  <Link
                    key={tag}
                    href={`/blog?tag=${encodeURIComponent(tag)}`}
                    className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition"
                  >
                    #{tag}
                  </Link>
                ))}
              </div>
            </motion.div>
          </div>
        </section>

        {/* Featured Image */}
        <section className="bg-white/60 backdrop-blur-sm">
          <div className="max-w-4xl mx-auto px-8">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.7, delay: 0.2 }}
              className="relative aspect-video rounded-lg overflow-hidden shadow-lg"
            >
              <Image
                src={post.featuredImage.url}
                alt={post.featuredImage.alt}
                fill
                className="object-cover"
                priority
              />
            </motion.div>
            {post.featuredImage.caption && (
              <p className="text-sm text-gray-500 text-center mt-4 italic">
                {post.featuredImage.caption}
              </p>
            )}
          </div>
        </section>

        {/* Divider Line */}
        <div className="border-b border-gray-200"></div>

        {/* Article Content */}
        <section className="bg-white py-16">
          <div className="max-w-4xl mx-auto px-8">
            <MarkdownRenderer
              content={post.content}
              className="prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-purple-600 prose-a:no-underline hover:prose-a:underline prose-strong:text-gray-900"
            />

            {/* Share Buttons */}
            <div className="mt-12 pt-8 border-t border-gray-200">
              <ShareButtons post={post} />
            </div>

            {/* Author Bio */}
            <div className="mt-12 p-6 bg-gray-50 rounded-xl border border-gray-200">
              <div className="flex items-start gap-4">
                <Image
                  src={post.author.avatar}
                  alt={post.author.name}
                  width={60}
                  height={60}
                  className="rounded-full"
                />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {post.author.name}
                  </h3>
                  <p className="text-gray-600 mb-3">{post.author.bio}</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Divider Line */}
        <div className="border-b border-gray-200"></div>

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <section className="bg-white py-16">
            <div className="max-w-6xl mx-auto px-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                Related Articles
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {relatedPosts.map((relatedPost, index) => (
                  <motion.div
                    key={relatedPost.id}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                  >
                    <BlogCard post={relatedPost} />
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
        )}
      </motion.article>
      <Footer />
    </>
  );
}
