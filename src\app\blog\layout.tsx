import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Website Development Blog | Expert Insights for Small Businesses | Sonata Sites",
  description: "Expert guidance, tips, and insights to help your small business succeed online. Learn why websites matter and how to make the most of your online presence.",
  keywords: [
    "small business website",
    "website development blog",
    "SEO tips",
    "digital marketing",
    "business growth",
    "website design",
    "online presence",
    "web development insights"
  ],
  authors: [{ name: "Sonata Sites Team" }],
  openGraph: {
    title: "Website Development Blog | Expert Insights for Small Businesses",
    description: "Expert guidance, tips, and insights to help your small business succeed online. Learn why websites matter and how to make the most of your online presence.",
    url: "https://sonatasites.com/blog",
    siteName: "Sonata Sites",
    images: [
      {
        url: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=630&fit=crop",
        width: 1200,
        height: 630,
        alt: "Website development insights and tips for small businesses",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Website Development Blog | Expert Insights for Small Businesses",
    description: "Expert guidance, tips, and insights to help your small business succeed online.",
    images: ["https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=630&fit=crop"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  alternates: {
    canonical: "https://sonatasites.com/blog",
  },
};

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
