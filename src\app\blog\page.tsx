"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Header from '../../components/Header';
import Footer from '../../components/Footer';
import BlogCard from '../../components/blog/BlogCard';
import BlogSearch from '../../components/blog/BlogSearch';
import BlogCategories from '../../components/blog/BlogCategories';
import BlogPagination from '../../components/blog/BlogPagination';
import LoadingSpinner from '../../components/LoadingSpinner';
import { searchPosts } from '../../lib/blog';
import { BlogListResponse, BlogSearchParams } from '../../types/blog';

export default function BlogPage() {
  const [blogData, setBlogData] = useState<BlogListResponse | null>(null);
  const [searchParams, setSearchParams] = useState<BlogSearchParams>({
    page: 1,
    limit: 6
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBlogData = async () => {
      setLoading(true);
      try {
        // Simulate API call delay for better UX
        await new Promise(resolve => setTimeout(resolve, 300));
        const data = searchPosts(searchParams);
        setBlogData(data);
      } catch (error) {
        console.error('Error fetching blog data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBlogData();
  }, [searchParams]);

  const handleSearch = (query: string) => {
    setSearchParams(prev => ({
      ...prev,
      query,
      page: 1
    }));
  };

  const handleCategoryFilter = (category: string | null) => {
    setSearchParams(prev => ({
      ...prev,
      category: category || undefined,
      page: 1
    }));
  };

  const handlePageChange = (page: number) => {
    setSearchParams(prev => ({
      ...prev,
      page
    }));
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  if (loading || !blogData) {
    return (
      <>
        <Header />
        <div className="min-h-screen bg-white">
          {/* Hero Section */}
          <section className="bg-gradient-to-br from-indigo-900 via-purple-900 to-indigo-800 text-white min-h-[60vh] flex items-center justify-center pt-32 pb-16">
            <div className="max-w-6xl mx-auto px-8 text-center">
              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7 }}
                className="text-4xl md:text-5xl font-bold mb-6"
              >
                Website Development Insights
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
                className="text-xl text-purple-100 max-w-4xl mx-auto leading-relaxed"
              >
                Expert guidance, tips, and insights to help your small business succeed online.&nbsp;Learn&nbsp;why websites matter and how to make the most of your online&nbsp;presence.
              </motion.p>
            </div>
          </section>

          {/* Loading Section */}
          <section className="py-32 bg-white">
            <div className="max-w-6xl mx-auto px-8 text-center">
              <LoadingSpinner size="lg" />
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="text-gray-600 mt-6 text-lg"
              >
                Loading articles...
              </motion.p>
            </div>
          </section>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Header />
      <motion.div
        className="min-h-screen bg-white"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-indigo-900 via-purple-900 to-indigo-800 text-white min-h-[60vh] flex items-center justify-center pt-32 pb-16">
          <div className="max-w-6xl mx-auto px-8 text-center">
            <motion.h1
              className="text-4xl md:text-5xl font-bold mb-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
            >
              Website Development Insights
            </motion.h1>
            <motion.p
              className="text-xl text-purple-100 max-w-4xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
            >
              Expert guidance, tips, and insights to help your small business succeed online.&nbsp;Learn&nbsp;why websites matter and how to make the most of your online&nbsp;presence.
            </motion.p>
          </div>
        </section>

        {/* Divider Line */}
        <div className="border-b border-gray-200"></div>

        {/* Search and Filters */}
        <section className="py-12 bg-white">
          <div className="max-w-6xl mx-auto px-8">
            <div className="bg-gray-50 rounded-xl shadow-md border border-gray-300 p-8 mb-8">
              <BlogSearch onSearch={handleSearch} />
              <BlogCategories
                categories={blogData.categories}
                selectedCategory={searchParams.category}
                onCategorySelect={handleCategoryFilter}
              />
            </div>
          </div>
        </section>

        {/* Divider Line */}
        <div className="border-b border-gray-200"></div>

        {/* Blog Posts */}
        <section className="pb-16 bg-white">
          <div className="max-w-6xl mx-auto px-8 pt-12">
            {blogData.posts.length === 0 ? (
              <div className="text-center py-16">
                <h3 className="text-2xl font-semibold text-gray-600 mb-4">
                  No articles found
                </h3>
                <p className="text-gray-500 mb-8">
                  Try adjusting your search or browse all categories.
                </p>
                <button
                  onClick={() => setSearchParams({ page: 1, limit: 6 })}
                  className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition"
                >
                  Show All Articles
                </button>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                  {blogData.posts.map((post, index) => (
                    <motion.div
                      key={post.id}
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                    >
                      <BlogCard post={post} />
                    </motion.div>
                  ))}
                </div>

                {/* Pagination */}
                {blogData.totalPages > 1 && (
                  <BlogPagination
                    currentPage={blogData.currentPage}
                    totalPages={blogData.totalPages}
                    onPageChange={handlePageChange}
                  />
                )}
              </>
            )}
          </div>
        </section>
      </motion.div>
      <Footer />
    </>
  );
}
