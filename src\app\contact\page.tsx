"use client";

import { motion } from "framer-motion";
import Header from "../../components/Header";
import Footer from "../../components/Footer";
import ContactForm from "../../components/ContactForm";

export default function Contact() {
  return (
    <>
      <Header />
      {/* Hero Section */}
      <section
        className="relative min-h-screen flex items-center justify-center"
        style={{
          backgroundImage:
            "linear-gradient(75deg, rgba(17, 17, 17, 0.9) 0%, rgba(74, 0, 224, 0.7) 100%), url('https://images.unsplash.com/photo-1423666639041-f56000c27a9a?auto=format&fit=crop&w=1200&q=80')",
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="relative z-10 w-full max-w-4xl mx-auto px-4 py-20 flex flex-col items-center text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            className="inline-block bg-white text-purple-700 font-bold rounded-full px-6 py-2 mb-6 shadow-lg text-lg"
          >
            Let&apos;s Talk Business
          </motion.div>
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.1 }}
            className="text-4xl md:text-5xl font-extrabold text-white mb-4 drop-shadow-lg"
          >
            Ready to Get Your Website Online?
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="text-white text-lg md:text-xl mb-8 max-w-2xl mx-auto"
          >
            Stop losing customers to competitors with better websites. Let&apos;s get you online fast with a site that actually works for your business.
          </motion.p>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 px-4 bg-indigo-950 text-white">
        <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -40 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7 }}
          >
            <h2 className="text-3xl font-bold mb-4">Get Your Free Quote</h2>
            <p className="mb-8 text-indigo-100">
              Fill out the form below and we&apos;ll get back to you within 2 hours with a custom quote for your project.
            </p>
            <ContactForm />
            <p className="text-xs text-indigo-200 mt-4">
              We respect your privacy. Your information will never be shared.
            </p>
          </motion.div>

          {/* Contact Info & Details */}
          <motion.div
            initial={{ opacity: 0, x: 40 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="space-y-8"
          >
            <div className="bg-white/10 rounded-2xl p-8">
              <h3 className="text-2xl font-semibold mb-4">Contact Information</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-bold">📧</span>
                  </div>
                  <div>
                    <p className="font-semibold">Email</p>
                    <p className="text-indigo-100"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-bold">📞</span>
                  </div>
                  <div>
                    <p className="font-semibold">Phone</p>
                    <p className="text-indigo-100">(*************</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-bold">⏰</span>
                  </div>
                  <div>
                    <p className="font-semibold">Response Time</p>
                    <p className="text-indigo-100">Within 2 hours, guaranteed</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-purple-500/20 to-indigo-500/20 rounded-2xl p-8 border border-purple-300/20">
              <h3 className="text-xl font-semibold mb-4">What Happens Next?</h3>
              <div className="space-y-3">
                <div className="flex items-start">
                  <span className="bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1">1</span>
                  <p className="text-indigo-100">We review your project details and business needs</p>
                </div>
                <div className="flex items-start">
                  <span className="bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1">2</span>
                  <p className="text-indigo-100">You get a custom quote within 2 hours</p>
                </div>
                <div className="flex items-start">
                  <span className="bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1">3</span>
                  <p className="text-indigo-100">We start building your site immediately after approval</p>
                </div>
                <div className="flex items-start">
                  <span className="bg-purple-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-1">4</span>
                  <p className="text-indigo-100">Your professional website is live in 48 hours</p>
                </div>
              </div>
            </div>

            <div className="bg-green-500/20 rounded-2xl p-6 border border-green-300/20">
              <h4 className="font-semibold text-green-100 mb-2">Our Promise</h4>
              <p className="text-green-100 text-sm">
                100% satisfaction guaranteed. If you&apos;re not happy with your website, we&apos;ll refund your money within 7 days. No questions asked.
              </p>
            </div>
          </motion.div>
        </div>
      </section>
      <Footer />
    </>
  );
}