"use client";

import { motion } from "framer-motion";
import { useState } from "react";
import Header from "../../components/Header";
import Footer from "../../components/Footer";

const faqs = [
  {
    question: "How fast can you really build my website?",
    answer: "We deliver most websites in 48 hours or less. Our Starter package ($99) is typically done in 24-48 hours, and our Business Pro package ($249) is completed within 48-72 hours. We work fast because we know time is money for small business owners."
  },
  {
    question: "What if I don't like my website?",
    answer: "Simple - we'll fix it for free or give you a full refund within 7 days. We've been doing this long enough to know what works, but if you're not happy, we make it right. No questions, no hassle."
  },
  {
    question: "Do I own my website or do you?",
    answer: "You own everything - the website, the domain, all content. We don't believe in holding your business hostage with monthly fees or ownership tricks. Once it's built and paid for, it's 100% yours."
  },
  {
    question: "Will my website work on phones?",
    answer: "Absolutely. Every website we build looks perfect on phones, tablets, and computers. More than half your customers will visit on their phone, so we make sure it works flawlessly on every device."
  },
  {
    question: "Can people find my website on Google?",
    answer: "Yes. We include basic SEO setup with every website so Google can find and index your site. For the Business Pro package, we also optimize for local search so people in your area can find you easily."
  },
  {
    question: "What's included in the $99 Starter package?",
    answer: "You get a 5-page professional website (Home, About, Services, Contact, plus one custom page), mobile-responsive design, contact form, basic SEO setup, and 48-hour delivery. Everything you need to look professional online."
  },
  {
    question: "What's the difference between Starter and Business Pro?",
    answer: "Business Pro ($249) includes everything in Starter plus: 10 pages instead of 5, advanced animations, Google Reviews integration, portfolio/gallery section, and priority 24-hour delivery. It's for businesses that want to really stand out."
  },
  {
    question: "Do you charge monthly fees?",
    answer: "No monthly fees from us. You pay once and own your website forever. You'll need web hosting (about $10-15/month) and a domain name (about $15/year), but those are industry-standard costs you'd pay anywhere."
  },
  {
    question: "Can you help with hosting and domains?",
    answer: "We can recommend reliable, affordable hosting providers and help you get set up. We'll even handle the technical setup for you so your website goes live smoothly."
  },
  {
    question: "What if I need changes after my website is done?",
    answer: "Minor tweaks and fixes are free for 30 days after delivery. For bigger changes or new features, we charge fair hourly rates. Most clients find they don't need changes because we get it right the first time."
  },
  {
    question: "Do you work with businesses in my industry?",
    answer: "We work with all types of small businesses - contractors, consultants, restaurants, retail stores, service providers, freelancers, and more. If you serve customers, we can build you a website that gets you more of them."
  },
  {
    question: "How do I get started?",
    answer: "Just fill out our contact form or call us at (970) 765-7934. We'll discuss your needs, give you a quote, and if you're ready to move forward, we can start building your website the same day."
  }
];

export default function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  return (
    <>
      <Header />
      {/* Hero Section */}
      <section
        className="relative min-h-screen flex items-center justify-center"
        style={{
          backgroundImage:
            "linear-gradient(75deg, rgba(17, 17, 17, 0.9) 0%, rgba(74, 0, 224, 0.7) 100%), url('https://images.unsplash.com/photo-**********-4b87b5e36e44?auto=format&fit=crop&w=1200&q=80')",
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="relative z-10 w-full max-w-4xl mx-auto px-4 py-20 flex flex-col items-center text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            className="inline-block bg-white text-purple-700 font-bold rounded-full px-6 py-2 mb-6 shadow-lg text-lg"
          >
            Questions & Answers
          </motion.div>
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.1 }}
            className="text-4xl md:text-5xl font-extrabold text-white mb-4 drop-shadow-lg"
          >
            Frequently Asked Questions
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="text-white text-lg md:text-xl mb-8 max-w-2xl mx-auto"
          >
            Get straight answers to the questions every small business owner asks about getting a professional website.
          </motion.p>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-indigo-900 mb-4">
              Everything You Need to Know
            </h2>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              We&apos;ve answered the most common questions about our website building process, pricing, and what you can expect when working with us.
            </p>
          </motion.div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow"
              >
                <button
                  className="w-full px-6 py-4 text-left flex justify-between items-center focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-inset rounded-lg"
                  onClick={() => setOpenIndex(openIndex === index ? null : index)}
                >
                  <h3 className="text-lg font-semibold text-indigo-900 pr-4">
                    {faq.question}
                  </h3>
                  <motion.span
                    animate={{ rotate: openIndex === index ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                    className="text-purple-500 text-2xl font-bold flex-shrink-0"
                  >
                    ↓
                  </motion.span>
                </button>
                <motion.div
                  initial={false}
                  animate={{
                    height: openIndex === index ? "auto" : 0,
                    opacity: openIndex === index ? 1 : 0
                  }}
                  transition={{ duration: 0.3 }}
                  className="overflow-hidden"
                >
                  <div className="px-6 pb-4">
                    <p className="text-gray-700 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7 }}
            className="text-center mt-16 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl p-8"
          >
            <h3 className="text-2xl font-bold text-indigo-900 mb-4">
              Still Have Questions?
            </h3>
            <p className="text-gray-600 mb-6">
              We&apos;re here to help. Contact us and we&apos;ll answer any questions you have about getting your website built.
            </p>
            <a
              href="/contact"
              className="inline-block bg-gradient-to-r from-purple-500 to-indigo-700 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:scale-105 transition text-lg"
            >
              Contact Us
            </a>
          </motion.div>
        </div>
      </section>
      <Footer />
    </>
  );
}