"use client";

import Header from "../components/Header";
import Hero from "../components/Hero";
import Services from "../components/Services";
import Portfolio from "../components/Portfolio";
import Pricing from "../components/Pricing";
import BlogSection from "../components/BlogSection";
import ContactForm from "../components/ContactForm";

const services = [
  {
    title: "Starter Website Package",
    description: "Our foundational $99 package. A professional, mobile-friendly site to get you online fast.",
  },
  {
    title: "E-Commerce Solutions",
    description: "Ready to sell online? We build secure, user-friendly online stores that make shopping a breeze.",
  },
  {
    title: "Portfolio & Showcase",
    description: "Perfect for creatives and contractors. Display your best work in a stunning, high-impact gallery.",
  },
];

const projects = [
  {
    image: "/elena.png",
    alt: "Freelance Photographer",
    title: "Service Business Site",
    subtitle: "Event Photographer",
    description: "A high-converting site for a local photographer, designed to turn visitors into paying clients. ",
    highlight: "Ready to grow? Your site could be next.",
    link: "https://elena-marchetti.vercel.app/"
  },
  {
    image: "/images/anon.png",
    alt: "E-Commerce Store",
    title: "E-Commerce Store",
    subtitle: "Online Sales",
    description: "A seamless shopping experience that builds trust and drives sales. ",
    highlight: "Imagine your products here, selling 24/7.",
    link: "https://anon-sonata.vercel.app/"
  },
  {
    image: "/freelancer.png",
    alt: "Freelancer Portfolio",
    title: "Freelancer Portfolio",
    subtitle: "Alex Lecky",
    description: "A stunning gallery to highlight your best work and attract new clients. ",
    highlight: "Let us help you make a memorable first impression.",
    link: "https://alexlecky.com"
  },
];

const pricing = [
  {
    title: "Starter",
    price: "$99",
    description: "Perfect for getting online quickly and professionally.",
    features: [
      "5-page professional website",
      "Mobile-responsive design",
      "Contact form integration",
      "Basic SEO setup",
      "48-hour delivery",
    ],
    button: {
      text: "Order Now",
      className: "inline-block bg-gradient-to-r from-purple-500 to-indigo-700 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:scale-105 transition text-lg",
    },
    cardClass: "bg-white rounded-lg shadow-lg p-10 text-center border-t-4 border-purple-500",
    priceClass: "text-4xl font-extrabold text-purple-600 mb-2",
    textClass: "text-indigo-900",
    featureTextClass: "text-gray-800",
  },
  {
    title: "Business Pro",
    price: "$249",
    description: "For growing businesses that want to stand out and impress.",
    features: [
      "10-page premium website",
      "Advanced animations",
      "Google Reviews integration",
      "Portfolio/gallery section",
      "Priority 24-hour delivery",
    ],
    button: {
      text: "Order Now",
      className: "inline-block bg-white text-indigo-900 px-8 py-3 rounded-full font-semibold shadow-lg hover:scale-105 transition text-lg",
    },
    cardClass: "bg-gradient-to-br from-indigo-700 to-purple-500 rounded-lg shadow-2xl p-12 text-center border-t-4 border-indigo-900 scale-105 text-white relative z-10",
    priceClass: "text-4xl font-extrabold mb-2",
    textClass: "",
    featureTextClass: "text-white",
  },
];

export default function Home() {
  return (
    <>
      <Header />
      <Hero />
      <Services services={services} />
      <Portfolio projects={projects} />
      <Pricing pricing={pricing} />
      <BlogSection />
      <section className="pt-24 bg-indigo-950 text-white">
        <div className="max-w-6xl mx-auto px-8 grid grid-cols-1 md:grid-cols-2 gap-16">
          <div>
            <h2 className="text-3xl font-bold mb-4">Contact Us</h2>
            <p className="mb-8 text-indigo-100">Ready to start your project or have questions? Fill out the form and we&apos;ll get back to you fast.</p>
            <ContactForm />
            <p className="text-xs text-indigo-200 mt-4">We respect your privacy. Your information will never be shared.</p>
          </div>
          <div className="space-y-8">
            <div>
              <h3 className="text-xl font-semibold mb-2">Sonata Sites</h3>
              <p className="text-indigo-100">We build professional websites that help small businesses succeed online. Fast, affordable, and effective.</p>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Quick Links</h3>
              <ul className="space-y-1">
                <li><a href="#services" className="hover:text-purple-400 transition">Services</a></li>
                <li><a href="#portfolio" className="hover:text-purple-400 transition">Projects</a></li>
                <li><a href="#pricing" className="hover:text-purple-400 transition">Pricing</a></li>
              </ul>
            </div>
            <div id="contact">
              <h3 className="text-xl font-semibold mb-2">Contact Info</h3>
              <p className="text-indigo-100">Email: <EMAIL></p>
              <p className="text-indigo-100">Phone: (*************</p>
              <p className="text-indigo-100">Ready to start? Let&apos;s talk.</p>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Our Guarantee</h3>
              <p className="text-indigo-100">We stand by our work. 100% satisfaction guaranteed or your money back within 7 days.</p>
            </div>
          </div>
        </div>
        <div className="border-t border-indigo-800 mt-16 pt-8 text-center text-indigo-200 text-sm">
          &copy; 2025 Sonata Sites. All Rights Reserved. At your service.
        </div>
      </section>
    </>
  );
}
