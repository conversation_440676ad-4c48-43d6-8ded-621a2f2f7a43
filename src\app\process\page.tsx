"use client";

import { motion } from "framer-motion";
import Header from "../../components/Header";
import Footer from "../../components/Footer";

const processSteps = [
  {
    step: "01",
    title: "You Contact Us",
    description: "Fill out our simple form or give us a call. Tell us about your business and what you need.",
    details: "We'll ask about your business, your goals, and what kind of website will help you get more customers. This takes about 5 minutes.",
    icon: "📞"
  },
  {
    step: "02", 
    title: "We Send Your Quote",
    description: "Within 2 hours, you'll get a custom quote with no surprises or hidden fees.",
    details: "We'll recommend either our Starter ($99) or Business Pro ($249) package based on your needs. The quote includes everything - no add-ons or surprise costs.",
    icon: "💰"
  },
  {
    step: "03",
    title: "You Approve & We Start",
    description: "Once you approve the quote, we start building your website immediately.",
    details: "We'll send you a simple agreement and invoice. As soon as you approve, our team starts working on your site. No waiting, no delays.",
    icon: "🚀"
  },
  {
    step: "04",
    title: "We Build Your Site",
    description: "Our team creates your professional website while you focus on running your business.",
    details: "We handle everything - design, content, mobile optimization, contact forms, and SEO setup. You don't need to do anything except run your business.",
    icon: "🔨"
  },
  {
    step: "05",
    title: "You Review & Approve",
    description: "We send you the completed website for review. Request any changes you need.",
    details: "You'll get a link to preview your new website. If you want any changes, just let us know and we'll make them quickly.",
    icon: "👀"
  },
  {
    step: "06",
    title: "Your Site Goes Live",
    description: "We launch your website and make sure everything works perfectly.",
    details: "We handle the technical setup, connect your domain, and make sure your site loads fast. You'll be online and ready for customers.",
    icon: "🌐"
  }
];

export default function Process() {
  return (
    <>
      <Header />
      {/* Hero Section */}
      <section
        className="relative min-h-screen flex items-center justify-center"
        style={{
          backgroundImage:
            "linear-gradient(75deg, rgba(17, 17, 17, 0.9) 0%, rgba(74, 0, 224, 0.7) 100%), url('https://images.unsplash.com/photo-1552664730-d307ca884978?auto=format&fit=crop&w=1200&q=80')",
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="relative z-10 w-full max-w-4xl mx-auto px-4 py-20 flex flex-col items-center text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            className="inline-block bg-white text-purple-700 font-bold rounded-full px-6 py-2 mb-6 shadow-lg text-lg"
          >
            How It Works
          </motion.div>
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.1 }}
            className="text-4xl md:text-5xl font-extrabold text-white mb-4 drop-shadow-lg"
          >
            From Idea to Online in 48 Hours
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="text-white text-lg md:text-xl mb-8 max-w-2xl mx-auto"
          >
            Our proven process gets you a professional website fast, without the hassle. Here&apos;s exactly how we make it happen.
          </motion.p>
        </div>
      </section>

      {/* Process Steps */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-indigo-900 mb-4">
              Simple. Fast. Professional.
            </h2>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              We&apos;ve streamlined our process to get you online as quickly as possible while ensuring every website meets our high standards.
            </p>
          </motion.div>

          <div className="space-y-16">
            {processSteps.map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ duration: 0.7, delay: index * 0.1 }}
                className={`flex flex-col lg:flex-row items-center gap-12 ${
                  index % 2 === 1 ? 'lg:flex-row-reverse' : ''
                }`}
              >
                {/* Content */}
                <div className="flex-1 space-y-4">
                  <div className="flex items-center gap-4">
                    <span className="text-6xl">{step.icon}</span>
                    <div>
                      <span className="text-purple-500 font-bold text-lg">Step {step.step}</span>
                      <h3 className="text-2xl md:text-3xl font-bold text-indigo-900">
                        {step.title}
                      </h3>
                    </div>
                  </div>
                  <p className="text-xl text-gray-700 font-medium">
                    {step.description}
                  </p>
                  <p className="text-gray-600 leading-relaxed">
                    {step.details}
                  </p>
                </div>

                {/* Visual Element */}
                <div className="flex-1 flex justify-center">
                  <div className="relative">
                    <div className="w-80 h-80 bg-gradient-to-br from-purple-100 to-indigo-100 rounded-full flex items-center justify-center shadow-lg">
                      <div className="w-64 h-64 bg-gradient-to-br from-purple-500 to-indigo-700 rounded-full flex items-center justify-center text-white">
                        <span className="text-8xl">{step.icon}</span>
                      </div>
                    </div>
                    <div className="absolute -top-4 -right-4 bg-white rounded-full w-16 h-16 flex items-center justify-center shadow-lg border-4 border-purple-500">
                      <span className="text-purple-700 font-bold text-lg">{step.step}</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Summary */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-indigo-900 mb-4">
              Your Timeline
            </h2>
            <p className="text-gray-600 text-lg">
              Here&apos;s what you can expect from start to finish
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl p-8"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="bg-purple-500 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  0h
                </div>
                <h3 className="font-semibold text-indigo-900 mb-2">You Contact Us</h3>
                <p className="text-gray-600 text-sm">Fill out our form or call us</p>
              </div>
              <div className="text-center">
                <div className="bg-purple-500 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  2h
                </div>
                <h3 className="font-semibold text-indigo-900 mb-2">Quote Delivered</h3>
                <p className="text-gray-600 text-sm">Custom quote in your inbox</p>
              </div>
              <div className="text-center">
                <div className="bg-purple-500 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  48h
                </div>
                <h3 className="font-semibold text-indigo-900 mb-2">Website Live</h3>
                <p className="text-gray-600 text-sm">Your professional site is online</p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gradient-to-r from-purple-500 to-indigo-700 py-16 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.7 }}
        >
          <h2 className="text-3xl md:text-4xl font-extrabold text-white mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-white text-lg mb-8 max-w-2xl mx-auto">
            Stop waiting and start growing. Contact us today and have your professional website online in 48 hours.
          </p>
          <a
            href="/contact"
            className="inline-block bg-white text-purple-700 font-semibold px-10 py-4 rounded-full shadow-lg hover:bg-purple-100 hover:scale-105 transition text-lg"
          >
            Start Your Project
          </a>
        </motion.div>
      </section>
      <Footer />
    </>
  );
}