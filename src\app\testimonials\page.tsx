"use client";

import { motion } from "framer-motion";
import Header from "../../components/Header";
import Footer from "../../components/Footer";
import Image from "next/image";

const testimonials = [
  {
    name: "<PERSON>",
    business: "Martinez Cleaning Services",
    location: "Denver, CO",
    quote: "I was getting maybe 2-3 calls a week from my Craigslist ads. After Sonata Sites built my website, I'm getting 15-20 calls a week. The phone hasn't stopped ringing!",
    result: "500% increase in leads",
    image: "https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
  },
  {
    name: "<PERSON>",
    business: "Thompson Handyman Services",
    location: "Fort Collins, CO",
    quote: "I tried building my own website for months and it looked terrible. These guys had me online in 2 days with something that actually looks professional. Worth every penny.",
    result: "Professional online presence in 48 hours",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=150&q=80"
  },
  {
    name: "Lisa Chen",
    business: "Chen's Tutoring",
    location: "Boulder, CO",
    quote: "Parents judge you by your website before they even meet you. Now I look as professional as the big tutoring companies, but I'm still the affordable local option.",
    result: "Doubled client bookings",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=150&q=80"
  },
  {
    name: "Carlos Rodriguez",
    business: "Rodriguez Landscaping",
    location: "Greeley, CO",
    quote: "My old website was built by my nephew and looked like it was from 1995. Now customers tell me my website convinced them to call me instead of my competitors.",
    result: "Winning jobs over competitors",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=150&q=80"
  },
  {
    name: "Jennifer Walsh",
    business: "Walsh Photography",
    location: "Loveland, CO",
    quote: "I needed a portfolio that would wow potential clients. The gallery they built showcases my work beautifully and I've booked 3 weddings just this month from website inquiries.",
    result: "3 wedding bookings in one month",
    image: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?auto=format&fit=crop&w=150&q=80"
  },
  {
    name: "David Kim",
    business: "Kim's Auto Repair",
    location: "Westminster, CO",
    quote: "People don't trust a mechanic without a good website anymore. Now I look legitimate and customers can easily find my hours, services, and contact info. Game changer.",
    result: "Increased customer trust and bookings",
    image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=150&q=80"
  }
];

const stats = [
  { number: "500+", label: "Websites Built" },
  { number: "48hrs", label: "Average Delivery" },
  { number: "99%", label: "Client Satisfaction" },
  { number: "0", label: "Monthly Fees" }
];

export default function Testimonials() {
  return (
    <>
      <Header />
      {/* Hero Section */}
      <section
        className="relative min-h-screen flex items-center justify-center"
        style={{
          backgroundImage:
            "linear-gradient(75deg, rgba(17, 17, 17, 0.9) 0%, rgba(74, 0, 224, 0.7) 100%), url('https://images.unsplash.com/photo-1521737604893-d14cc237f11d?auto=format&fit=crop&w=1200&q=80')",
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="relative z-10 w-full max-w-4xl mx-auto px-4 py-20 flex flex-col items-center text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            className="inline-block bg-white text-purple-700 font-bold rounded-full px-6 py-2 mb-6 shadow-lg text-lg"
          >
            Real Results
          </motion.div>
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.1 }}
            className="text-4xl md:text-5xl font-extrabold text-white mb-4 drop-shadow-lg"
          >
            What Our Clients Say
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="text-white text-lg md:text-xl mb-8 max-w-2xl mx-auto"
          >
            Don&apos;t just take our word for it. Here&apos;s what real small business owners say about working with Sonata Sites.
          </motion.p>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4">
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={{
              hidden: {},
              visible: { transition: { staggerChildren: 0.1 } },
            }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8"
          >
            {stats.map((stat) => (
              <motion.div
                key={stat.label}
                variants={{
                  hidden: { opacity: 0, y: 30 },
                  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
                }}
                className="text-center"
              >
                <div className="text-4xl md:text-5xl font-extrabold text-purple-600 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Testimonials Grid */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-indigo-900 mb-4">
              Success Stories from Real Businesses
            </h2>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              These are actual clients who trusted us with their online presence. See how a professional website transformed their businesses.
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.2 }}
            variants={{
              hidden: {},
              visible: { transition: { staggerChildren: 0.15 } },
            }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {testimonials.map((testimonial) => (
              <motion.div
                key={testimonial.name}
                variants={{
                  hidden: { opacity: 0, y: 40 },
                  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
                }}
                className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-shadow"
              >
                <div className="flex items-center mb-6">
                  <Image
                    src={testimonial.image}
                    alt={testimonial.name}
                    width={600} height={400}
                    className="w-16 h-16 rounded-full object-cover mr-4"
                  />
                  <div>
                    <h3 className="font-semibold text-indigo-900">{testimonial.name}</h3>
                    <p className="text-purple-600 font-medium">{testimonial.business}</p>
                    <p className="text-gray-500 text-sm">{testimonial.location}</p>
                  </div>
                </div>
                <blockquote className="text-gray-700 mb-4 italic leading-relaxed">
                  &quot;{testimonial.quote}&quot;
                </blockquote>
                <div className="bg-green-50 rounded-lg p-3 border-l-4 border-green-400">
                  <p className="text-green-700 font-semibold text-sm">
                    Result: {testimonial.result}
                  </p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Before/After Section */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-indigo-900 mb-4">
              The Difference a Professional Website Makes
            </h2>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              See the transformation from DIY disasters to professional websites that actually work.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.7 }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
          >
            <div className="bg-red-50 rounded-2xl p-8 border-2 border-red-200">
              <h3 className="text-2xl font-bold text-red-700 mb-4">❌ Before: DIY Disasters</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start">
                  <span className="text-red-500 mr-3">•</span>
                  Looks unprofessional and outdated
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-3">•</span>
                  Doesn&apos;t work on mobile phones
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-3">•</span>
                  Hard to find on Google
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-3">•</span>
                  Customers don&apos;t trust you
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-3">•</span>
                  Losing business to competitors
                </li>
              </ul>
            </div>

            <div className="bg-green-50 rounded-2xl p-8 border-2 border-green-200">
              <h3 className="text-2xl font-bold text-green-700 mb-4">✅ After: Professional Results</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start">
                  <span className="text-green-500 mr-3">•</span>
                  Looks as good as the big companies
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3">•</span>
                  Perfect on all devices
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3">•</span>
                  Shows up in local searches
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3">•</span>
                  Builds instant credibility
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3">•</span>
                  More leads and customers
                </li>
              </ul>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gradient-to-r from-purple-500 to-indigo-700 py-16 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.7 }}
        >
          <h2 className="text-3xl md:text-4xl font-extrabold text-white mb-4">
            Ready to Join Our Success Stories?
          </h2>
          <p className="text-white text-lg mb-8 max-w-2xl mx-auto">
            Stop losing customers to competitors with better websites. Get your professional site online in 48 hours.
          </p>
          <a
            href="/contact"
            className="inline-block bg-white text-purple-700 font-semibold px-10 py-4 rounded-full shadow-lg hover:bg-purple-100 hover:scale-105 transition text-lg"
          >
            Get Your Website
          </a>
        </motion.div>
      </section>
      <Footer />
    </>
  );
}