"use client";

import { motion } from 'framer-motion';
import Link from 'next/link';
import BlogCard from './blog/BlogCard';
import { getFeaturedPosts } from '../lib/blog';

export default function BlogSection() {
  const featuredPosts = getFeaturedPosts(3);

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-6xl mx-auto px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.7 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Website Development Insights
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Expert guidance and insights to help your small business succeed online. 
            Learn why websites matter and how to make the most of your digital presence.
          </p>
          <Link
            href="/blog"
            className="inline-flex items-center gap-2 text-purple-600 font-semibold hover:text-purple-700 transition-colors"
          >
            View All Articles
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </motion.div>

        {/* Featured Posts Grid */}
        {featuredPosts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {featuredPosts.map((post, index) => (
              <motion.div
                key={post.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.3 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <BlogCard post={post} featured={true} />
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="bg-white rounded-lg shadow-sm p-12">
              <h3 className="text-2xl font-semibold text-gray-600 mb-4">
                Coming Soon
              </h3>
              <p className="text-gray-500 mb-8">
                We&apos;re working on creating valuable content to help your business succeed online.
              </p>
              <Link
                href="/contact"
                className="inline-block bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition"
              >
                Get Notified When We Publish
              </Link>
            </div>
          </div>
        )}

        {/* Call to Action */}
        <motion.div
          className="bg-gradient-to-br from-purple-600 to-indigo-700 rounded-2xl p-8 md:p-12 text-center text-white"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.7, delay: 0.3 }}
        >
          <h3 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Get Your Business Online?
          </h3>
          <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
            Don&apos;t let another day pass without a professional website.
            Join hundreds of small businesses that trust us to grow their online presence.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/#pricing"
              className="bg-white text-purple-600 px-8 py-4 rounded-lg font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              View Our Packages
            </Link>
            <Link
              href="/contact"
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-bold hover:bg-white hover:text-purple-600 transition-all duration-300"
            >
              Get Free Consultation
            </Link>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
