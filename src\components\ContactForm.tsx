"use client";
import React, { useState } from "react";

interface FormData {
  name: string;
  email: string;
  message: string;
}

export default function ContactForm() {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <form
      onSubmit={async (e) => {
        e.preventDefault();
        setLoading(true);
        setError(null);
        setSuccess(false);
        
        // Basic validation
        if (!formData.name.trim() || !formData.email.trim() || !formData.message.trim()) {
          setError("Please fill in all fields");
          setLoading(false);
          return;
        }
        
        const submitData = new FormData();
        submitData.append('name', formData.name);
        submitData.append('email', formData.email);
        submitData.append('message', formData.message);
        
        try {
          const res = await fetch("https://formspree.io/f/xldnkqlz", {
            method: "POST",
            headers: { "Accept": "application/json" },
            body: submitData,
          });
          if (res.ok) {
            setSuccess(true);
            setFormData({ name: '', email: '', message: '' });
          } else {
            const result = await res.json();
            setError(result.errors?.[0]?.message || "Something went wrong. Please try again.");
          }
        } catch {
          setError("Network error. Please try again.");
        } finally {
          setLoading(false);
        }
      }}
      className="space-y-6 bg-white/10 p-8 rounded-lg shadow-lg"
    >
      <div>
        <label htmlFor="name" className="block mb-2 font-semibold">Name</label>
        <input 
          type="text" 
          id="name" 
          name="name" 
          value={formData.name}
          onChange={handleInputChange}
          required 
          className="w-full px-4 py-2 rounded bg-white/80 text-indigo-900 focus:outline-none focus:ring-2 focus:ring-purple-500" 
        />
      </div>
      <div>
        <label htmlFor="email" className="block mb-2 font-semibold">Email</label>
        <input 
          type="email" 
          id="email" 
          name="email" 
          value={formData.email}
          onChange={handleInputChange}
          required 
          className="w-full px-4 py-2 rounded bg-white/80 text-indigo-900 focus:outline-none focus:ring-2 focus:ring-purple-500" 
        />
      </div>
      <div>
        <label htmlFor="message" className="block mb-2 font-semibold">Message</label>
        <textarea 
          id="message" 
          name="message" 
          value={formData.message}
          onChange={handleInputChange}
          rows={4} 
          required 
          className="w-full px-4 py-2 rounded bg-white/80 text-indigo-900 focus:outline-none focus:ring-2 focus:ring-purple-500" 
        />
      </div>
      <button type="submit" className="w-full bg-gradient-to-r from-purple-500 to-indigo-700 text-white py-3 rounded-full font-semibold shadow-lg hover:scale-105 transition" disabled={loading}>
        {loading ? "Sending..." : "Send Message"}
      </button>
      {success && (
        <div className="text-green-400 font-semibold text-center">Thank you! Your message has been sent.</div>
      )}
      {error && (
        <div className="text-red-400 font-semibold text-center">{error}</div>
      )}
    </form>
  );
} 