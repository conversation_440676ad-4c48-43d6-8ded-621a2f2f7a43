import React from "react";
import Link from "next/link";

export default function Footer() {
  return (
    <footer id="contact" className="bg-indigo-950 text-white pt-24 pb-8 mt-0">
      <div className="max-w-6xl mx-auto px-8 grid grid-cols-1 md:grid-cols-3 gap-12">
        {/* Business Details */}
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-2">Sonata Sites</h3>
            <p className="text-indigo-100">
              We build professional websites that help small businesses succeed online. Fast, affordable, and effective.
            </p>
          </div>
          <div>
            <h3 className="text-xl font-semibold mb-2">Our Guarantee</h3>
            <p className="text-indigo-100">
              We stand by our work. 100% satisfaction guaranteed or your money back within 7 days.
            </p>
          </div>
        </div>

        {/* Navigation Links */}
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-4">Main Sections</h3>
            <ul className="space-y-2">
              {[
                { href: "/#services", label: "Services" },
                { href: "/#portfolio", label: "Projects" },
                { href: "/#pricing", label: "Pricing" },
                { href: "/#contact", label: "Contact" },
              ].map(({ href, label }) => (
                <li key={label}>
                  <Link
                    href={href}
                    className="text-indigo-100 hover:text-white transition-colors duration-300 relative inline-block group"
                  >
                    {label}
                    <span className="absolute left-0 bottom-0 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-full"></span>
                  </Link>
                </li>
              ))}
              <li>
                <Link
                  href="/blog"
                  className="text-indigo-100 hover:text-white transition-colors duration-300 relative inline-block group"
                >
                  Blog
                  <span className="absolute left-0 bottom-0 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-full"></span>
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-4">Pages</h3>
            <ul className="space-y-2">
              {[
                { href: "/", label: "Home" },
                { href: "/about", label: "About" },
                { href: "/process", label: "Process" },
                { href: "/faq", label: "FAQ" },
                { href: "/additional-services", label: "Additional Services" },
                { href: "/testimonials", label: "Testimonials" },
              ].map(({ href, label }) => (
                <li key={label}>
                  <Link
                    href={href}
                    className="text-indigo-100 hover:text-white transition-colors duration-300 relative inline-block group"
                  >
                    {label}
                    <span className="absolute left-0 bottom-0 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-full"></span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Contact Info */}
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-4">Contact Info</h3>
            <div className="space-y-2 text-indigo-100">
              <p>
                <a
                  href="mailto:<EMAIL>"
                  className="hover:text-white transition-colors duration-300 relative inline-block group"
                >
                  <EMAIL>
                  <span className="absolute left-0 bottom-0 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-full"></span>
                </a>
              </p>
              <p>
                <a
                  href="tel:+19707657934"
                  className="hover:text-white transition-colors duration-300 relative inline-block group"
                >
                  (*************
                  <span className="absolute left-0 bottom-0 w-0 h-0.5 bg-purple-400 transition-all duration-300 group-hover:w-full"></span>
                </a>
              </p>
              <p className="mt-4">Ready to start? Let&apos;s talk.</p>
            </div>
          </div>

          <div>
            <h3 className="text-xl font-semibold mb-4">Get Started</h3>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-purple-500 to-indigo-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-purple-600 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              Start Your Project
            </Link>
          </div>
        </div>
      </div>

      <div className="border-t border-indigo-800 mt-16 pt-8 text-center text-indigo-200 text-sm">
        &copy; 2025 Sonata Sites. All Rights Reserved. At your service.
      </div>
    </footer>
  );
}
