"use client";

import { useEffect, useState } from "react";
import <PERSON>rip<PERSON> from "next/script";

declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'js' | 'consent',
      targetId: string | Date,
      config?: Record<string, unknown>
    ) => void;
    dataLayer: Record<string, unknown>[];
  }
}

interface GoogleAnalyticsProps {
  measurementId: string;
}

export default function GoogleAnalytics({ measurementId }: GoogleAnalyticsProps) {
  const [hasConsent, setHasConsent] = useState(false);

  useEffect(() => {
    // Check if analytics cookies are consented to
    const checkConsent = () => {
      if (typeof window === "undefined") return false;

      const consent = localStorage.getItem("cookie-consent");
      if (consent) {
        try {
          const preferences = JSON.parse(consent);
          return preferences.analytics === true;
        } catch {
          return false;
        }
      }
      return false;
    };

    // Set initial consent state
    setHasConsent(checkConsent());

    // Listen for storage changes (when user updates cookie preferences)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "cookie-consent") {
        const newConsent = checkConsent();
        setHasConsent(newConsent);

        if (typeof window !== "undefined" && window.gtag) {
          window.gtag("consent", "update", {
            analytics_storage: newConsent ? "granted" : "denied",
          });
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  // Don't render scripts if no consent
  if (!hasConsent) {
    return null;
  }

  return (
    <>
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${measurementId}', {
            page_title: document.title,
            page_location: window.location.href,
          });
        `}
      </Script>
    </>
  );
}
