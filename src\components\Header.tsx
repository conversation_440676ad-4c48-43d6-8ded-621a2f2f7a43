"use client";
import React, { useEffect, useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import { usePathname } from "next/navigation";

const extraPages = [
  { name: "Blog", href: "/blog" },
  { name: "About", href: "/about" },
  { name: "Process", href: "/process" },
  { name: "FAQ", href: "/faq" },
  { name: "Additional Services", href: "/additional-services" },
  { name: "Testimonials", href: "/testimonials" },
  { name: "Contact", href: "/contact" },
];

export default function Header() {
  const headerRef = useRef<HTMLDivElement>(null);
  const [headerVisible, setHeaderVisible] = useState(false);
  const [desktopMenuOpen, setDesktopMenuOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname()
  const isHomePage = pathname === "/" || pathname === "/home";
  const isBlogPage = pathname.startsWith("/blog");

  useEffect(() => {
    setTimeout(() => setHeaderVisible(true), 400);

    const handleScroll = () => {
      const scrolled = window.scrollY > 50;

      if (headerRef.current) {
        // Always show scrolled state on blog pages, or when actually scrolled
        if (scrolled || isBlogPage) {
          headerRef.current.classList.add("scrolled");
        } else {
          headerRef.current.classList.remove("scrolled");
        }
      }
    };

    // Initial call to set the correct state
    handleScroll();

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [isBlogPage]);

  // Close mobile menu on click outside (desktop menu closes on mouse leave)
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      if (
        mobileMenuOpen &&
        headerRef.current &&
        !headerRef.current.contains(e.target as Node)
      ) {
        setMobileMenuOpen(false);
      }
    };
    if (mobileMenuOpen) document.addEventListener("mousedown", handleClick);
    return () => document.removeEventListener("mousedown", handleClick);
  }, [mobileMenuOpen]);

  return (
    <motion.header
      ref={headerRef}
      className="header fixed w-full top-0 z-50 transition-all duration-400 bg-transparent text-white py-6"
      initial={{ opacity: 0, y: -30 }}
      animate={headerVisible ? { opacity: 1, y: 0 } : {}}
      transition={{ duration: 0.7 }}
    >
      <nav className="nav-container max-w-6xl mx-auto flex justify-between items-center px-8">
        <div className="logo text-2xl md:text-3xl font-extrabold tracking-tight">
          <Link href="/">Sonata Sites</Link>
        </div>
        <ul className="nav-menu hidden md:flex gap-10 font-semibold text-lg items-center">
          <li><a href={isHomePage ? "#home" : "/#home"} className="hover:text-purple-400 transition">Home</a></li>
          <li><a href={isHomePage ? "#services" : "/#services"} className="hover:text-purple-400 transition">Services</a></li>
          <li><a href={isHomePage ? "#portfolio" : "/#portfolio"} className="hover:text-purple-400 transition">Projects</a></li>
          <li><a href={isHomePage ? "#pricing" : "/#pricing"} className="hover:text-purple-400 transition">Pricing</a></li>
          <li><a href={isHomePage ? "#contact" : "/#contact"} className="hover:text-purple-400 transition">Contact</a></li>

          {/* More Dropdown at the end */}
          <li
            className="relative"
            onMouseEnter={() => setDesktopMenuOpen(true)}
            onMouseLeave={() => setDesktopMenuOpen(false)}
          >
            <button
              aria-label="More pages"
              aria-haspopup="true"
              aria-expanded={desktopMenuOpen}
              className="flex flex-col justify-center items-center w-10 h-10 focus:outline-none"
            >
              <motion.span
                className="block w-7 h-1 bg-white rounded mb-1"
                animate={desktopMenuOpen ? { rotate: 45, y: 8 } : { rotate: 0, y: 0 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              />
              <motion.span
                className="block w-7 h-1 bg-white rounded mb-1"
                animate={desktopMenuOpen ? { opacity: 0 } : { opacity: 1 }}
                transition={{ duration: 0.2 }}
              />
              <motion.span
                className="block w-7 h-1 bg-white rounded"
                animate={desktopMenuOpen ? { rotate: -45, y: -8 } : { rotate: 0, y: 0 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              />
            </button>
            <AnimatePresence>
              {desktopMenuOpen && (
                <motion.ul
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="absolute right-0 mt-2 w-56 bg-white text-indigo-900 rounded-lg shadow-lg py-2 z-50 max-h-[70vh] overflow-y-auto"
                >
                  {extraPages.map((page) => (
                    <li key={page.name}>
                      <Link
                        href={page.href}
                        className="block px-6 py-3 hover:bg-purple-100 transition"
                        onClick={() => setDesktopMenuOpen(false)}
                      >
                        {page.name}
                      </Link>
                    </li>
                  ))}
                </motion.ul>
              )}
            </AnimatePresence>
          </li>
        </ul>
        {/* Mobile Hamburger */}
        <div className="md:hidden flex items-center">
          <button
            aria-label="Open menu"
            aria-haspopup="true"
            aria-expanded={mobileMenuOpen}
            className="flex flex-col justify-center items-center w-10 h-10 focus:outline-none"
            onClick={() => setMobileMenuOpen((v) => !v)}
          >
            <motion.span
              className="block w-7 h-1 bg-white rounded mb-1"
              animate={mobileMenuOpen ? { rotate: 45, y: 8 } : { rotate: 0, y: 0 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            />
            <motion.span
              className="block w-7 h-1 bg-white rounded mb-1"
              animate={mobileMenuOpen ? { opacity: 0 } : { opacity: 1 }}
              transition={{ duration: 0.2 }}
            />
            <motion.span
              className="block w-7 h-1 bg-white rounded"
              animate={mobileMenuOpen ? { rotate: -45, y: -8 } : { rotate: 0, y: 0 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            />
          </button>
          <AnimatePresence>
            {mobileMenuOpen && (
              <motion.ul
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
                className="absolute right-8 top-20 w-56 bg-white text-indigo-900 rounded-lg shadow-lg py-2 z-50 max-h-[calc(100vh-6rem)] overflow-y-auto"
              >
                <li><a href="#home" className="block px-6 py-3 hover:bg-purple-100 transition" onClick={() => setMobileMenuOpen(false)}>Home</a></li>
                <li><a href="#services" className="block px-6 py-3 hover:bg-purple-100 transition" onClick={() => setMobileMenuOpen(false)}>Services</a></li>
                <li><a href="#portfolio" className="block px-6 py-3 hover:bg-purple-100 transition" onClick={() => setMobileMenuOpen(false)}>Projects</a></li>
                <li><a href="#pricing" className="block px-6 py-3 hover:bg-purple-100 transition" onClick={() => setMobileMenuOpen(false)}>Pricing</a></li>
                <li><a href="#contact" className="block px-6 py-3 hover:bg-purple-100 transition" onClick={() => setMobileMenuOpen(false)}>Contact</a></li>
                <li>
                  <div className="border-t border-indigo-100 my-2" />
                </li>
                {extraPages.map((page) => (
                  <li key={page.name}>
                    <Link
                      href={page.href}
                      className="block px-6 py-3 hover:bg-purple-100 transition"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      {page.name}
                    </Link>
                  </li>
                ))}
              </motion.ul>
            )}
          </AnimatePresence>
        </div>
        {headerVisible && (
          <motion.a
            href="#pricing"
            className="cta-button hidden md:inline-block bg-gradient-to-r from-purple-500 to-indigo-700 text-white px-7 py-3 rounded-full font-bold shadow-lg hover:scale-105 transition text-lg"
            style={{ boxShadow: "0 5px 15px rgba(142, 45, 226, 0.4)" }}
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
          >
            Get a Quote
          </motion.a>
        )}
      </nav>
      <style>{`
        .header.scrolled {
          background-color: #1a1a2e;
          box-shadow: 0 4px 20px rgba(0,0,0,0.2);
          padding-top: 0.5rem;
          padding-bottom: 0.5rem;
          transition: background-color 0.4s, padding 0.4s;
        }
      `}</style>
    </motion.header>
  );
}