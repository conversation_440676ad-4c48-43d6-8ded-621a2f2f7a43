import React from "react";
import { motion } from "framer-motion";
import Image from "next/image";

export default function Hero() {
  return (
    <motion.section
      id="home"
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.6 }}
      transition={{ duration: 0.7 }}
      className="hero relative pt-30 pb-50 min-h-[80vh] flex items-center overflow-hidden"
    >
      {/* Background Image using Next.js Image component */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/hero.jpg"
          alt="Hero background"
          fill
          className="object-cover"
          priority
          quality={90}
        />
      </div>

      {/* Gradient overlay */}
      <div
        className="absolute inset-0 z-[1]"
        style={{
          background: "linear-gradient(75deg, rgba(17, 17, 17, 0.9) 0%, rgba(74, 0, 224, 0.7) 100%)"
        }}
      />
      {/* Angled bottom overlay to replace clip-path */}
      <div
        className="absolute bottom-0 left-0 w-full h-32 bg-white z-[3]"
        style={{
          clipPath: "polygon(0 100%, 100% 0%, 100% 100%)"
        }}
      />
      <div className="hero-content max-w-6xl mx-auto px-8 relative z-[2] flex flex-col justify-center h-full min-h-[60vh] items-start text-white md:text-left text-center w-full">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.7 }}
          transition={{ delay: 0.2, duration: 0.7 }}
          className="tag inline-block bg-[rgba(255,255,255,0.15)] text-white-700 px-6 py-2 rounded-full font-bold mb-8 text-base tracking-wide shadow-md/30 shadow-lg md:mx-0 mx-auto"
        >
          High-Performance Websites
        </motion.div>
        <motion.h1
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.7 }}
          transition={{ delay: 0.3, duration: 0.7 }}
          className="text-4xl md:text-6xl font-extrabold mb-6 max-w-2xl leading-tight drop-shadow-lg text-white"
        >
          Build a Digital Presence That Drives Results
        </motion.h1>
        <motion.p
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.7 }}
          transition={{ delay: 0.4, duration: 0.7 }}
          className="hero-subtitle text-lg md:text-2xl mb-10 max-w-xl text-white font-medium"
        >
          Stop losing customers to DIY disasters. Get a professional, fast, and affordable website designed to convert visitors into loyal clients.
        </motion.p>
        <motion.a
          href="#portfolio"
          whileHover={{ scale: 1.08 }}
          whileTap={{ scale: 0.97 }}
          className="btn btn-primary inline-block bg-gradient-to-r from-purple-500 to-indigo-700 text-white px-10 py-4 rounded-full font-bold shadow-lg hover:scale-105 transition text-lg md:mx-0 mx-auto"
          style={{ boxShadow: "0 5px 15px rgba(142, 45, 226, 0.4)" }}
        >
          See Our Work <span className="arrow ml-2">→</span>
        </motion.a>
      </div>
    </motion.section>
  );
} 