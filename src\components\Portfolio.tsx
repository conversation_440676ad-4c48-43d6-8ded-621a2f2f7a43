import React from "react";
import { motion } from "framer-motion";
import Image from "next/image";

type Project = {
  image: string;
  alt: string;
  title: string;
  subtitle: string;
  description: string;
  highlight: string;
  link: string;
};

type PortfolioProps = {
  projects: Project[];
};

export default function Portfolio({ projects }: PortfolioProps) {
  const handleProjectClick = (link: string) => {
    window.open(link, '_blank', 'noopener,noreferrer');
  };

  return (
    <section id="portfolio" className="py-24 bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 via-purple-400/10 to-pink-400/10"></div>
      <div className="absolute top-20 left-10 w-72 h-72 bg-blue-400/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-400/20 rounded-full blur-3xl"></div>

      <div className="max-w-7xl mx-auto px-8 relative z-10">
        <motion.h2
          className="text-3xl md:text-4xl font-bold text-center text-indigo-900 mb-4"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.7 }}
          transition={{ duration: 0.7 }}
        >
          See What We&apos;ve Built
        </motion.h2>
        <motion.p
          className="text-center text-gray-600 max-w-2xl mx-auto mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.7 }}
          transition={{ duration: 0.7, delay: 0.15 }}
        >
          Explore a selection of our recent projects and discover how we help businesses like yours stand out online. Each website is crafted with care, strategy, and a focus on results. Whether you need to generate leads, sell products, or showcase your work, we have a solution tailored for you. <span className="font-semibold text-indigo-700">Your business deserves a website that works as hard as you do.</span>
        </motion.p>
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={{
            hidden: {},
            visible: { transition: { staggerChildren: 0.18 } },
          }}
        >
          {projects.map((project) => (
            <motion.div
              key={project.title}
              className="relative group cursor-pointer"
              variants={{
                hidden: { opacity: 0, y: 40 },
                visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
              }}
              onClick={() => handleProjectClick(project.link)}
            >
              {/* Glassmorphism card */}
              <div className="relative rounded-2xl overflow-hidden backdrop-blur-md bg-white/70 border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 hover:scale-[1.02] group">
                {/* Card glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-pink-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>

                {/* Image container with better sizing */}
                <div className={`relative h-64 lg:h-72 p-6 ${project.image === '/elena.png' ? 'bg-gradient-to-br from-[#f8f3e9]/80 to-[#f0e6d2]/80' : 'bg-gradient-to-br from-white/80 to-gray-50/80'} backdrop-blur-sm`}>
                  <Image
                    src={project.image}
                    alt={project.alt}
                    width={600}
                    height={400}
                    className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-500"
                  />
                </div>

                {/* Content overlay with glassmorphism */}
                <div className="absolute inset-0 flex flex-col justify-end p-6 bg-gradient-to-t from-black/90 via-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 backdrop-blur-sm">
                  <div className="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                    <h3 className="text-xl lg:text-2xl font-bold text-white mb-2 drop-shadow-lg">{project.title}</h3>
                    <p className="text-white/90 text-sm lg:text-base mb-3 drop-shadow-md">{project.subtitle}</p>
                    <p className="text-white/80 text-xs lg:text-sm leading-relaxed drop-shadow-md">
                      {project.description}
                      <span className="font-semibold text-white">{project.highlight}</span>
                    </p>
                  </div>
                </div>

                {/* Bottom accent line */}
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
} 