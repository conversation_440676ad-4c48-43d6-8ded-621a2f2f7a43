import React from "react";
import { motion } from "framer-motion";

type PricingPlan = {
  title: string;
  price: string;
  description: string;
  features: string[];
  button: {
    text: string;
    className: string;
  };
  cardClass: string;
  priceClass: string;
  textClass: string;
  featureTextClass: string;
};

type PricingProps = {
  pricing: PricingPlan[];
};

export default function Pricing({ pricing }: PricingProps) {
  return (
    <section id="pricing" className="py-24 bg-white">
      <div className="max-w-6xl mx-auto px-8">
        <motion.h2
          className="text-3xl md:text-4xl font-bold text-center text-indigo-900 mb-4"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.7 }}
          transition={{ duration: 0.7 }}
        >
          Simple, Honest Pricing
        </motion.h2>
        <motion.p
          className="text-center text-gray-600 max-w-2xl mx-auto mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.7 }}
          transition={{ duration: 0.7, delay: 0.15 }}
        >
          No hidden fees, no surprises. Pay once and own your website forever. Choose the plan that fits your goals and let us handle the rest. <span className="font-semibold text-indigo-700">Invest in your business with confidence.</span>
        </motion.p>
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8 items-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          variants={{
            hidden: {},
            visible: { transition: { staggerChildren: 0.18 } },
          }}
        >
          {pricing.map((plan) => (
            <motion.div
              key={plan.title}
              className={plan.cardClass}
              variants={{
                hidden: { opacity: 0, y: 40 },
                visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
              }}
            >
              <h3 className={`text-2xl font-bold mb-2 ${plan.textClass}`}>{plan.title}</h3>
              <div className={plan.priceClass}>{plan.price}</div>
              <p className={`mb-6 ${plan.textClass}`}>{plan.description}</p>
              <ul className={`text-left mb-8 space-y-2 ${plan.featureTextClass}`}>
                {plan.features.map((feature) => (
                  <li key={feature} className="relative pl-6">
                    <span className={`absolute left-0 font-bold ${plan.title === 'Business Pro' ? "text-white" : "text-green-500"}`}>✓</span> {feature}
                  </li>
                ))}
              </ul>
              <a href="#contact" className={plan.button.className}>{plan.button.text}</a>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
} 