"use client";

import { motion } from 'framer-motion';
import { BlogCategory } from '../../types/blog';

interface BlogCategoriesProps {
  categories: BlogCategory[];
  selectedCategory?: string;
  onCategorySelect: (category: string | null) => void;
}

export default function BlogCategories({
  categories,
  selectedCategory,
  onCategorySelect
}: BlogCategoriesProps) {
  return (
    <motion.div
      className="flex flex-wrap gap-3"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
    >
      {/* All Categories Button */}
      <button
        onClick={() => onCategorySelect(null)}
        className={`px-4 py-2 rounded-full font-medium transition-all duration-200 ${
          !selectedCategory
            ? 'bg-purple-600 text-white shadow-md'
            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
        }`}
      >
        All Articles
      </button>

      {/* Category Buttons */}
      {categories.map((category) => (
        <button
          key={category.id}
          onClick={() => onCategorySelect(category.slug)}
          className={`px-4 py-2 rounded-full font-medium transition-all duration-200 ${
            selectedCategory === category.slug
              ? `${category.color} text-white shadow-md`
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          {category.name}
        </button>
      ))}
    </motion.div>
  );
}
