"use client";

import React from 'react';
import { motion } from 'framer-motion';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export default function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
  const parseMarkdown = (text: string): React.ReactElement[] => {
    const lines = text.split('\n');
    const elements: React.ReactElement[] = [];
    let currentIndex = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Skip empty lines
      if (line.trim() === '') {
        continue;
      }

      // Headers
      if (line.startsWith('# ')) {
        elements.push(
          <motion.h1
            key={currentIndex++}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: currentIndex * 0.1 }}
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-8 leading-tight"
          >
            {line.substring(2)}
          </motion.h1>
        );
      } else if (line.startsWith('## ')) {
        elements.push(
          <motion.h2
            key={currentIndex++}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: currentIndex * 0.1 }}
            className="text-3xl md:text-4xl font-bold text-gray-900 mb-6 mt-12 leading-tight"
          >
            {line.substring(3)}
          </motion.h2>
        );
      } else if (line.startsWith('### ')) {
        elements.push(
          <motion.h3
            key={currentIndex++}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: currentIndex * 0.1 }}
            className="text-2xl md:text-3xl font-bold text-gray-900 mb-4 mt-8 leading-tight"
          >
            {parseInlineMarkdown(line.substring(4))}
          </motion.h3>
        );
      } else if (line.startsWith('#### ')) {
        elements.push(
          <motion.h4
            key={currentIndex++}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: currentIndex * 0.1 }}
            className="text-xl md:text-2xl font-semibold text-gray-900 mb-3 mt-6"
          >
            {parseInlineMarkdown(line.substring(5))}
          </motion.h4>
        );
      }
      // Lists
      else if (line.startsWith('- ')) {
        const listItems = [line];
        let j = i + 1;

        // Collect consecutive list items
        while (j < lines.length && lines[j].startsWith('- ')) {
          listItems.push(lines[j]);
          j++;
        }

        elements.push(
          <motion.ul
            key={currentIndex++}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: currentIndex * 0.1 }}
            className="list-none space-y-3 mb-6 ml-4"
          >
            {listItems.map((item, index) => (
              <li key={index} className="flex items-start gap-3">
                <span className="w-2 h-2 bg-purple-600 rounded-full mt-2 flex-shrink-0"></span>
                <span className="text-gray-700 leading-relaxed">
                  {parseInlineMarkdown(item.substring(2))}
                </span>
              </li>
            ))}
          </motion.ul>
        );

        i = j - 1; // Skip the processed list items
      }
      // Numbered lists
      else if (/^\d+\.\s/.test(line)) {
        const listItems = [line];
        let j = i + 1;

        // Collect consecutive numbered list items
        while (j < lines.length && /^\d+\.\s/.test(lines[j])) {
          listItems.push(lines[j]);
          j++;
        }

        elements.push(
          <motion.ol
            key={currentIndex++}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: currentIndex * 0.1 }}
            className="space-y-3 mb-6 ml-4"
          >
            {listItems.map((item, index) => (
              <li key={index} className="flex items-start gap-3">
                <span className="w-6 h-6 bg-purple-600 text-white text-sm font-semibold rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  {index + 1}
                </span>
                <span className="text-gray-700 leading-relaxed">
                  {parseInlineMarkdown(item.replace(/^\d+\.\s/, ''))}
                </span>
              </li>
            ))}
          </motion.ol>
        );

        i = j - 1; // Skip the processed list items
      }
      // Blockquotes
      else if (line.startsWith('> ')) {
        elements.push(
          <motion.blockquote
            key={currentIndex++}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: currentIndex * 0.1 }}
            className="border-l-4 border-gray-400 pl-6 py-4 my-6 bg-gray-50 rounded-r-lg border-r border-t border-b border-gray-200"
          >
            <p className="text-gray-700 italic text-lg leading-relaxed">
              {parseInlineMarkdown(line.substring(2))}
            </p>
          </motion.blockquote>
        );
      }
      // Code blocks
      else if (line.startsWith('```')) {
        const codeLines = [];
        let j = i + 1;

        // Collect code block content
        while (j < lines.length && !lines[j].startsWith('```')) {
          codeLines.push(lines[j]);
          j++;
        }

        if (j < lines.length) { // Found closing ```
          elements.push(
            <motion.div
              key={currentIndex++}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: currentIndex * 0.1 }}
              className="my-6 bg-gray-900 rounded-lg overflow-hidden border border-white/10"
            >
              <div className="bg-gray-800 px-4 py-2 text-gray-300 text-sm font-medium">
                Code
              </div>
              <pre className="p-4 overflow-x-auto">
                <code className="text-gray-100 text-sm font-mono leading-relaxed">
                  {codeLines.join('\n')}
                </code>
              </pre>
            </motion.div>
          );
          i = j; // Skip to after closing ```
        }
      }
      // Horizontal rules
      else if (line.trim() === '---' || line.trim() === '***') {
        elements.push(
          <motion.hr
            key={currentIndex++}
            initial={{ opacity: 0, scaleX: 0 }}
            animate={{ opacity: 1, scaleX: 1 }}
            transition={{ duration: 0.8, delay: currentIndex * 0.1 }}
            className="my-8 border-0 h-px bg-gray-300"
          />
        );
      }
      // Call-out boxes (lines starting with !)
      else if (line.startsWith('! ')) {
        elements.push(
          <motion.div
            key={currentIndex++}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: currentIndex * 0.1 }}
            className="my-6 p-4 bg-blue-50 border-l-4 border-blue-500 rounded-r-lg shadow-sm"
          >
            <p className="text-blue-800 font-medium leading-relaxed">
              💡 {parseInlineMarkdown(line.substring(2))}
            </p>
          </motion.div>
        );
      }
      // Regular paragraphs
      else {
        elements.push(
          <motion.p
            key={currentIndex++}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: currentIndex * 0.1 }}
            className="text-gray-700 leading-relaxed mb-6 text-lg"
          >
            {parseInlineMarkdown(line)}
          </motion.p>
        );
      }
    }

    return elements;
  };

  const parseInlineMarkdown = (text: string): React.ReactNode => {
    // Handle bold text **text**
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>');

    // Handle italic text *text*
    text = text.replace(/\*(.*?)\*/g, '<em class="italic text-gray-600">$1</em>');

    // Handle links [text](url)
    text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-purple-600 hover:text-purple-700 hover:underline font-medium transition-colors">$1</a>');

    // Handle inline code `code`
    text = text.replace(/`([^`]+)`/g, '<code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono border border-gray-200">$1</code>');

    // Handle strikethrough ~~text~~
    text = text.replace(/~~(.*?)~~/g, '<del class="text-gray-400 line-through">$1</del>');

    // Handle highlights ==text==
    text = text.replace(/==(.*?)==/g, '<mark class="bg-yellow-200 text-yellow-900 px-1 py-0.5 rounded">$1</mark>');

    return <span dangerouslySetInnerHTML={{ __html: text }} />;
  };

  return (
    <div className={`prose prose-lg max-w-none ${className}`}>
      {parseMarkdown(content)}
    </div>
  );
}
