"use client";

import { BlogPost } from '../../types/blog';

interface StructuredDataProps {
  post: BlogPost;
}

export default function StructuredData({ post }: StructuredDataProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": post.title,
    "description": post.excerpt,
    "image": [
      post.featuredImage.url
    ],
    "datePublished": post.publishedAt,
    "dateModified": post.updatedAt || post.publishedAt,
    "author": {
      "@type": "Person",
      "name": post.author.name,
    },
    "publisher": {
      "@type": "Organization",
      "name": "Sonata Sites",
      "logo": {
        "@type": "ImageObject",
        "url": "https://sonatasites.com/icon.png"
      }
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://sonatasites.com/blog/${post.slug}`
    },
    "articleSection": post.category.name,
    "keywords": post.tags.join(", "),
    "wordCount": post.content.split(' ').length,
    "timeRequired": `PT${post.readTime}M`,
    "inLanguage": "en-US"
  };

  const breadcrumbStructuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://sonatasites.com"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Blog",
        "item": "https://sonatasites.com/blog"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": post.title,
        "item": `https://sonatasites.com/blog/${post.slug}`
      }
    ]
  };

  const organizationStructuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Sonata Sites",
    "url": "https://sonatasites.com",
    "logo": "https://sonatasites.com/icon.png",
    "description": "We build professional websites that help small businesses succeed online. Fast, affordable, and effective.",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-765-7934",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "sameAs": [
      "https://linkedin.com/company/sonata-sites"
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbStructuredData),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationStructuredData),
        }}
      />
    </>
  );
}
