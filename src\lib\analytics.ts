// Google Analytics utility functions

declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'js' | 'consent',
      targetId: string | Date,
      config?: Record<string, unknown>
    ) => void;
  }
}

// Check if user has consented to analytics
export const hasAnalyticsConsent = (): boolean => {
  if (typeof window === "undefined") return false;
  
  const consent = localStorage.getItem("cookie-consent");
  if (consent) {
    try {
      const preferences = JSON.parse(consent);
      return preferences.analytics === true;
    } catch {
      return false;
    }
  }
  return false;
};

// Track page views
export const trackPageView = (url: string, title?: string): void => {
  if (!hasAnalyticsConsent() || typeof window === "undefined" || !window.gtag) {
    return;
  }

  window.gtag("config", process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID!, {
    page_title: title || document.title,
    page_location: url,
  });
};

// Track custom events
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number
): void => {
  if (!hasAnalyticsConsent() || typeof window === "undefined" || !window.gtag) {
    return;
  }

  window.gtag("event", action, {
    event_category: category,
    event_label: label,
    value: value,
  });
};

// Track form submissions
export const trackFormSubmission = (formName: string): void => {
  trackEvent("form_submit", "engagement", formName);
};

// Track button clicks
export const trackButtonClick = (buttonName: string, location?: string): void => {
  const label = location ? `${buttonName} - ${location}` : buttonName;
  trackEvent("click", "engagement", label);
};

// Track file downloads
export const trackDownload = (fileName: string): void => {
  trackEvent("download", "engagement", fileName);
};
