import { BlogPost, BlogCategory, BlogSearchParams, BlogListResponse } from '../types/blog';
import { blogPosts, blogCategories } from '../data/blog';

export function getAllPosts(): BlogPost[] {
  return blogPosts
    .filter(post => post.published)
    .sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());
}

export function getFeaturedPosts(limit: number = 3): BlogPost[] {
  return getAllPosts()
    .filter(post => post.featured)
    .slice(0, limit);
}

export function getPostBySlug(slug: string): BlogPost | null {
  return blogPosts.find(post => post.slug === slug && post.published) || null;
}

export function getRelatedPosts(currentPost: BlogPost, limit: number = 3): BlogPost[] {
  return getAllPosts()
    .filter(post => 
      post.id !== currentPost.id && 
      (post.category.id === currentPost.category.id || 
       post.tags.some(tag => currentPost.tags.includes(tag)))
    )
    .slice(0, limit);
}

export function searchPosts(params: BlogSearchParams): BlogListResponse {
  const { query, category, tag, page = 1, limit = 6 } = params;
  let filteredPosts = getAllPosts();

  // Filter by search query
  if (query) {
    const searchTerm = query.toLowerCase();
    filteredPosts = filteredPosts.filter(post =>
      post.title.toLowerCase().includes(searchTerm) ||
      post.excerpt.toLowerCase().includes(searchTerm) ||
      post.content.toLowerCase().includes(searchTerm) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  }

  // Filter by category
  if (category) {
    filteredPosts = filteredPosts.filter(post => post.category.slug === category);
  }

  // Filter by tag
  if (tag) {
    filteredPosts = filteredPosts.filter(post => 
      post.tags.some(postTag => postTag.toLowerCase() === tag.toLowerCase())
    );
  }

  // Pagination
  const totalPosts = filteredPosts.length;
  const totalPages = Math.ceil(totalPosts / limit);
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedPosts = filteredPosts.slice(startIndex, endIndex);

  // Get popular tags
  const allTags = getAllPosts().flatMap(post => post.tags);
  const tagCounts = allTags.reduce((acc, tag) => {
    acc[tag] = (acc[tag] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const popularTags = Object.entries(tagCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([tag]) => tag);

  return {
    posts: paginatedPosts,
    totalPosts,
    totalPages,
    currentPage: page,
    categories: blogCategories,
    popularTags
  };
}

export function getPostsByCategory(categorySlug: string, limit?: number): BlogPost[] {
  const posts = getAllPosts().filter(post => post.category.slug === categorySlug);
  return limit ? posts.slice(0, limit) : posts;
}

export function getPostsByTag(tag: string, limit?: number): BlogPost[] {
  const posts = getAllPosts().filter(post => 
    post.tags.some(postTag => postTag.toLowerCase() === tag.toLowerCase())
  );
  return limit ? posts.slice(0, limit) : posts;
}

export function getAllCategories(): BlogCategory[] {
  return blogCategories;
}

export function getAllTags(): string[] {
  const allTags = getAllPosts().flatMap(post => post.tags);
  return [...new Set(allTags)].sort();
}

export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

export function generateBlogUrl(slug: string): string {
  return `/blog/${slug}`;
}

export function generateCategoryUrl(categorySlug: string): string {
  return `/blog?category=${categorySlug}`;
}

export function generateTagUrl(tag: string): string {
  return `/blog?tag=${encodeURIComponent(tag)}`;
}
